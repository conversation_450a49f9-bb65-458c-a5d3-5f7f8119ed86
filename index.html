<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOP MCP：一行提示词，AI 驱动您的易宝支付极速集成！</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="YOP MCP (模型上下文协议) 是易宝支付为开发者打造的创新工具集，通过AI助手实现支付API的极速集成，将数天工作缩短至小时级。">
    <meta name="keywords" content="YOP MCP, 易宝支付, AI集成, 支付API, 开发者工具, 模型上下文协议">
    <meta name="author" content="易宝支付">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="YOP MCP：AI 驱动的易宝支付极速集成">
    <meta property="og:description" content="专为开发者打造的全新集成体验，通过自然语言提示词实现支付API的智能对接。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://yop-mcp.yeepay.com">
    <meta property="og:image" content="https://yop-mcp.yeepay.com/assets/og-image.jpg">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="YOP MCP：AI 驱动的易宝支付极速集成">
    <meta name="twitter:description" content="专为开发者打造的全新集成体验，通过自然语言提示词实现支付API的智能对接。">
    <meta name="twitter:image" content="https://yop-mcp.yeepay.com/assets/twitter-image.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/apple-touch-icon.png">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://fonts.googleapis.com">

    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <!-- Font Awesome via CDN -->
    <link rel="stylesheet" href="assets/font-awesome/6.7.2/css/all.min.css">

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/styles.css">

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "YOP MCP",
        "description": "YOP MCP (模型上下文协议) 是易宝支付为开发者打造的创新工具集，通过AI助手实现支付API的极速集成。",
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": "Cross-platform",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        },
        "publisher": {
            "@type": "Organization",
            "name": "易宝支付",
            "url": "https://www.yeepay.com"
        },
        "downloadUrl": "https://pypi.org/project/yop-mcp/",
        "softwareVersion": "1.0.0",
        "releaseNotes": "首个正式版本，支持AI驱动的支付API集成"
    }
    </script>
</head>
<body class="bg-gray-100 text-gray-800">
    <!-- 滚动进度条 -->
    <div id="scroll-progress" class="scroll-progress-bar"></div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
                <i class="fas fa-cubes text-white text-2xl"></i>
            </div>
            <div class="text-white text-xl font-semibold mb-2">YOP MCP</div>
            <div class="text-white/80 text-sm">正在加载...</div>
            <div class="w-32 h-1 bg-white/20 rounded-full mt-4 overflow-hidden">
                <div class="h-full bg-white rounded-full animate-pulse" style="width: 100%; animation: loading-progress 2s ease-in-out;"></div>
            </div>
        </div>
    </div>
    <!-- 顶部导航栏 -->
    <nav class="bg-white/80 backdrop-blur-md shadow-sm fixed w-full z-50 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="flex items-center nav-logo-icon-wrapper group">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-2 shadow-md nav-logo-icon transition-all duration-300 group-hover:shadow-lg">
                            <i class="fas fa-cubes text-white text-xl"></i>
                        </div>
                        <span class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent group-hover:from-blue-500 group-hover:to-purple-500 transition-all duration-300">YOP MCP</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#features" class="text-gray-600 hover:text-blue-600 transition-colors nav-link-hover">核心功能</a>
                    <a href="#how-it-works" class="text-gray-600 hover:text-blue-600 transition-colors nav-link-hover">工作原理</a>
                    <a href="#prerequisites" class="text-gray-600 hover:text-blue-600 transition-colors nav-link-hover">准备工作</a>
                    <a href="#quick-start" class="text-gray-600 hover:text-blue-600 transition-colors nav-link-hover">快速开始</a>
                    <a href="https://github.com/yop-platform/yop-mcp" target="_blank"
                       class="text-gray-500 hover:text-blue-600 transition-colors text-2xl">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-blue-600 focus:outline-none">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white shadow-lg py-2">
            <a href="#features" class="block px-4 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600">核心功能</a>
            <a href="#how-it-works" class="block px-4 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600">工作原理</a>
            <a href="#prerequisites" class="block px-4 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600">准备工作</a>
            <a href="#quick-start" class="block px-4 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600">快速开始</a>
            <a href="https://github.com/yop-platform/yop-mcp" target="_blank" class="block px-4 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600">
                <i class="fab fa-github mr-2"></i> GitHub
            </a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg hero-pattern hero-section-enhanced tech-grid-enhanced min-h-screen flex items-center overflow-hidden relative pt-16">
        <!-- Modern tech background elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <!-- 简化的科技线条动画 - 只保留两条 -->
            <div class="tech-lines">
                <div class="tech-line"></div>
                <div class="tech-line"></div>
            </div>

            <!-- 极简浮动粒子 - 只保留少量关键粒子 -->
            <div class="tech-particles">
                <div class="tech-particle" style="left: 20%; animation-delay: 0s;"></div>
                <div class="tech-particle" style="left: 60%; animation-delay: 15s;"></div>
                <div class="tech-particle" style="left: 80%; animation-delay: 30s;"></div>
            </div>

            <!-- 极简几何形状 - 恢复蓝色静态元素 -->
            <div class="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-blue-400/3 to-blue-600/2 rounded-full blur-3xl"></div>
            <div class="absolute bottom-32 right-1/4 w-24 h-24 bg-gradient-to-br from-purple-400/2 to-purple-600/1 rounded-full blur-3xl animate-pulse" style="animation-delay: 4s; animation-duration: 10s;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="lg:flex lg:items-center lg:justify-between lg:gap-12 min-h-[80vh]">
                <div class="lg:w-3/5">
                    <div class="mb-6">
                        <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-cyan-500/30 to-blue-600/30 backdrop-blur-sm border border-cyan-300/50 text-white text-sm font-medium mb-6 fade-in-up shadow-lg">
                            <i class="fas fa-sparkles mr-2 text-yellow-400"></i>
                            🚀 AI 驱动的支付集成革命
                        </div>
                    </div>
                    <h1 id="hero-title" class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight hero-title-enhanced">
                        YOP MCP 来了！<br>AI 赋能，一行提示词接入易宝支付。
                    </h1>
                    <p class="text-xl mb-10 leading-relaxed hero-subtitle-enhanced">
                        专为开发者打造的全新集成体验，将数天的对接工作缩短至小时级，甚至分钟级。告别繁琐，拥抱智能，让AI成为您的易宝支付集成伙伴！
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="#quick-start"
                           class="modern-button glow-effect hero-button-primary inline-flex items-center justify-center px-10 py-4 text-lg font-bold rounded-xl text-white bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 hover:from-cyan-300 hover:via-blue-400 hover:to-purple-500 transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-cyan-500/50 border-2 border-cyan-300/30 hover:border-cyan-300/60"
                           aria-label="跳转到快速上手配置YOP MCP部分"
                           style="box-shadow: 0 0 30px rgba(6, 182, 212, 0.5), 0 8px 25px rgba(0, 0, 0, 0.3); animation: primaryButtonGlow 2s ease-in-out infinite alternate;">
                            <i class="fas fa-rocket mr-3 text-xl"></i>
                            🚀 立即配置
                        </a>
                        <a href="#how-it-works"
                           class="modern-button inline-flex items-center justify-center px-8 py-3.5 text-base font-medium rounded-lg text-white bg-gradient-to-r from-slate-600 via-slate-700 to-slate-800 hover:from-slate-500 hover:via-slate-600 hover:to-slate-700 border-2 border-slate-400/50 hover:border-slate-300/70 transition-all duration-300 transform hover:scale-105 backdrop-blur-sm shadow-lg hover:shadow-xl"
                           aria-label="了解YOP MCP工作原理">
                            <i class="fas fa-cogs mr-2 text-lg"></i>
                            🔧 了解工作原理
                        </a>
                    </div>

                    <!-- Stats section -->
                    <div class="mt-12 grid grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold hero-stats-enhanced mb-1">5分钟</div>
                            <div class="text-sm hero-stats-label-enhanced">快速配置</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold hero-stats-enhanced mb-1">1400+</div>
                            <div class="text-sm hero-stats-label-enhanced">API接口</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold hero-stats-enhanced mb-1">24/7</div>
                            <div class="text-sm hero-stats-label-enhanced">AI助手</div>
                        </div>
                    </div>
                </div>
                <div class="lg:w-2/5 mt-12 lg:mt-0 fade-in-up flex items-start justify-center" data-delay="3" style="padding-top: 6vh;">
                    <div class="relative floating-animation w-full">
                        <!-- Dynamic AI Workflow Demonstration -->
                        <div class="relative space-y-6">
                            <!-- Step 1: Natural Language Input -->
                            <div class="ai-workflow-step relative bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-4 border border-blue-200/50">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center shadow-md">
                                        <i class="fas fa-comments text-white text-sm"></i>
                                    </div>
                                    <span class="ml-3 text-sm font-medium text-slate-700">自然语言提示</span>
                                    <div class="ml-auto">
                                        <div class="w-2 h-2 bg-green-500 rounded-full enhanced-pulse"></div>
                                    </div>
                                </div>
                                <div class="bg-slate-50 rounded-lg p-3 border border-slate-200 status-indicator">
                                    <p class="text-sm text-slate-600 font-mono">"请帮我实现易宝支付的订单支付功能"</p>
                                </div>
                                <!-- Arrow -->
                                <div class="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                                    <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-md workflow-arrow">
                                        <i class="fas fa-arrow-down text-white text-xs"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2: AI Processing -->
                            <div class="ai-workflow-step relative bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-4 border border-purple-200/50">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-md">
                                        <i class="fas fa-brain text-white text-sm"></i>
                                    </div>
                                    <span class="ml-3 text-sm font-medium text-slate-700">AI 智能分析</span>
                                    <div class="ml-auto flex items-center">
                                        <i class="fas fa-spinner fa-spin text-purple-500 text-sm mr-2"></i>
                                        <span class="text-xs text-purple-600">处理中...</span>
                                    </div>
                                </div>
                                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-3 border border-purple-200 status-indicator">
                                    <div class="flex items-center space-x-2 text-sm text-purple-700 font-medium">
                                        <i class="fas fa-cogs"></i>
                                        <span>通过 YOP MCP 分析最佳实现方案</span>
                                    </div>
                                    <div class="flex items-center space-x-2 text-sm text-purple-600 mt-2 font-medium">
                                        <i class="fas fa-database"></i>
                                        <span>获取最新 API 文档和最佳实践</span>
                                    </div>
                                </div>
                                <!-- Arrow -->
                                <div class="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                                    <div class="w-6 h-6 bg-gradient-to-br from-purple-500 to-green-500 rounded-full flex items-center justify-center shadow-md workflow-arrow" style="animation-delay: 0.5s;">
                                        <i class="fas fa-arrow-down text-white text-xs"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Code Generation -->
                            <div class="ai-workflow-step relative bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-4 border border-green-200/50">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-md">
                                        <i class="fas fa-code text-white text-sm"></i>
                                    </div>
                                    <span class="ml-3 text-sm font-medium text-slate-700">代码生成</span>
                                    <div class="ml-auto flex items-center">
                                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2 enhanced-pulse"></div>
                                        <span class="text-xs text-green-600">完成</span>
                                    </div>
                                </div>
                                <div class="ai-code-block bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg p-4 border border-green-500/30 shadow-lg">
                                    <div class="flex items-center mb-3">
                                        <div class="w-2 h-2 bg-green-400 rounded-full mr-2 enhanced-pulse"></div>
                                        <span class="text-green-400 text-sm font-medium">Python 代码</span>
                                        <div class="ml-auto flex items-center space-x-2">
                                            <span class="text-xs text-green-500">✓ 生成完成</span>
                                            <button class="text-gray-400 hover:text-green-400 transition-colors copy-code-btn" title="复制代码" onclick="copyCode('ai-generated-code')">
                                                <i class="fas fa-copy text-xs"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <pre class="bg-slate-900/50 rounded-md p-3 overflow-x-auto"><code id="ai-generated-code" class="block text-green-300 text-sm leading-relaxed font-mono language-python"><span class="text-blue-400">from</span> <span class="text-yellow-300">yop</span> <span class="text-blue-400">import</span> <span class="text-yellow-300">YopClient</span>

<span class="text-gray-400"># 初始化客户端</span>
<span class="text-purple-400">client</span> = <span class="text-yellow-300">YopClient</span>(<span class="text-green-400">"your_app_key"</span>)

<span class="text-gray-400"># 创建支付订单</span>
<span class="text-purple-400">response</span> = <span class="text-purple-400">client</span>.<span class="text-cyan-400">pay</span>.<span class="text-cyan-400">create</span>({
    <span class="text-green-400">"order_id"</span>: <span class="text-green-400">"12345"</span>,
    <span class="text-green-400">"amount"</span>: <span class="text-orange-400">100.00</span>,
    <span class="text-green-400">"currency"</span>: <span class="text-green-400">"CNY"</span>
})

<span class="text-blue-400">print</span>(<span class="text-green-400">f"支付结果: {response.status}"</span>)</code></pre>
                                </div>
                            </div>

                            <!-- Floating Icons - 恢复蓝色静态图标 -->
                            <div class="floating-icon absolute -top-4 -right-4 w-10 h-10 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full flex items-center justify-center shadow-lg opacity-80">
                                <i class="fas fa-bolt text-white text-sm"></i>
                            </div>
                            <div class="floating-icon absolute top-1/3 -left-6 w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center shadow-lg opacity-60">
                                <i class="fas fa-magic text-white text-xs"></i>
                            </div>
                            <div class="floating-icon absolute bottom-10 -right-6 w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-400 rounded-full flex items-center justify-center shadow-lg opacity-70">
                                <i class="fas fa-rocket text-white text-sm"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Features Section -->
    <section class="py-20 section-bg-pattern relative overflow-hidden" id="features">
        <!-- Background decorative elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-10 right-10 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
            <div class="absolute bottom-20 left-10 w-48 h-48 bg-purple-500/5 rounded-full blur-2xl"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-500/3 to-purple-500/3 rounded-full blur-3xl"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16 fade-in-up">
                <div class="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium mb-6">
                    <i class="fas fa-magic mr-2"></i>
                    核心功能
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    释放 AI 的力量，革命化您的支付集成
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    YOP MCP (模型上下文协议) 是易宝支付为开发者精心打造的创新技术与工具集。
                </p>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    通过YOP MCP，AI将成为您最得力的支付集成助手，化繁为简，提效增速。
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Feature 1 -->
                <div class="glassmorphism glow-border p-6 rounded-xl card-shadow hover-scale group fade-in-up feature-card-enhanced shimmer-effect" data-delay="0">
                    <div class="w-14 h-14 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg flex items-center justify-center mb-5 feature-icon-glow shadow-glow" style="--glow-color: 0, 242, 254;">
                        <i class="fas fa-bolt text-white text-2xl transition-transform duration-300 group-hover:scale-110"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">极速高效</h3>
                    <p class="text-gray-200 mb-4 text-sm">
                        从需求分析到代码生成，AI全程加速，将数天工作缩短至<span class="text-cyan-300 font-semibold">小时乃至分钟级</span>。
                    </p>
                    <div class="space-y-1.5 text-sm">
                        <div class="flex items-center text-gray-300"><i class="fas fa-check-circle text-cyan-400 mr-2"></i>自动化需求分析</div>
                        <div class="flex items-center text-gray-300"><i class="fas fa-check-circle text-cyan-400 mr-2"></i>智能代码生成</div>
                    </div>
                    <div class="mt-4 text-cyan-300 text-sm font-medium">数天 → 小时级 <i class="fas fa-arrow-right ml-1 workflow-arrow"></i></div>
                </div>

                <!-- Feature 2 -->
                <div class="glassmorphism glow-border p-6 rounded-xl card-shadow hover-scale group fade-in-up feature-card-enhanced shimmer-effect" data-delay="1">
                    <div class="w-14 h-14 bg-gradient-to-br from-emerald-400 to-green-600 rounded-lg flex items-center justify-center mb-5 feature-icon-glow shadow-glow" style="--glow-color: 16, 185, 129;">
                        <i class="fas fa-magic text-white text-2xl transition-transform duration-300 group-hover:scale-110"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">简单易用</h3>
                    <p class="text-gray-200 mb-4 text-sm">
                        告别繁复文档，用<span class="text-emerald-300 font-semibold">自然语言</span>驱动复杂API对接，无需记忆SDK细节。
                    </p>
                     <div class="space-y-1.5 text-sm">
                        <div class="flex items-center text-gray-300"><i class="fas fa-check-circle text-emerald-400 mr-2"></i>自然语言交互</div>
                        <div class="flex items-center text-gray-300"><i class="fas fa-check-circle text-emerald-400 mr-2"></i>无需记忆API细节</div>
                    </div>
                    <div class="mt-4 text-emerald-300 text-sm font-medium">自然语言驱动 <i class="fas fa-comments ml-1 workflow-arrow"></i></div>
                </div>

                <!-- Feature 3 -->
                <div class="glassmorphism glow-border p-6 rounded-xl card-shadow hover-scale group fade-in-up feature-card-enhanced shimmer-effect" data-delay="2">
                    <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-indigo-600 rounded-lg flex items-center justify-center mb-5 feature-icon-glow shadow-glow" style="--glow-color: 147, 51, 234;">
                        <i class="fas fa-brain text-white text-2xl transition-transform duration-300 group-hover:scale-110"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">智能强大</h3>
                    <p class="text-gray-200 mb-4 text-sm">
                        AI深度理解意图，结合<span class="text-purple-300 font-semibold">实时YOP文档</span>与最佳实践，提供精准方案与高质量代码。
                    </p>
                    <div class="space-y-1.5 text-sm">
                        <div class="flex items-center text-gray-300"><i class="fas fa-check-circle text-purple-400 mr-2"></i>深度意图理解</div>
                        <div class="flex items-center text-gray-300"><i class="fas fa-check-circle text-purple-400 mr-2"></i>最佳实践知识库</div>
                    </div>
                    <div class="mt-4 text-purple-300 text-sm font-medium">实时文档同步 <i class="fas fa-sync-alt ml-1 workflow-arrow"></i></div>
                </div>

                <!-- Feature 4 -->
                <div class="glassmorphism glow-border p-6 rounded-xl card-shadow hover-scale group fade-in-up feature-card-enhanced shimmer-effect" data-delay="3">
                    <div class="w-14 h-14 bg-gradient-to-br from-red-400 to-pink-600 rounded-lg flex items-center justify-center mb-5 feature-icon-glow shadow-glow" style="--glow-color: 220, 38, 38;">
                        <i class="fas fa-shield-alt text-white text-2xl transition-transform duration-300 group-hover:scale-110"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">安全可控</h3>
                    <p class="text-gray-200 mb-4 text-sm">
                        核心工具<span class="text-red-300 font-semibold">本地执行</span>，敏感信息本地生成存储，标准I/O交互增强控制性。
                    </p>
                    <div class="space-y-1.5 text-sm">
                        <div class="flex items-center text-gray-300"><i class="fas fa-check-circle text-red-400 mr-2"></i>本地密钥生成</div>
                        <div class="flex items-center text-gray-300"><i class="fas fa-check-circle text-red-400 mr-2"></i>标准输入输出交互</div>
                    </div>
                    <div class="mt-4 text-red-300 text-sm font-medium">本地执行 <i class="fas fa-lock ml-1 workflow-arrow"></i></div>
                </div>
            </div>

            <!-- YOP MCP 工作流程图 -->
            <div class="mt-24 fade-in-up">
                <div class="text-center mb-12">
                    <div class="inline-flex items-center px-4 py-2 rounded-full bg-purple-100 text-purple-800 text-sm font-medium mb-6">
                        <i class="fas fa-cogs mr-2"></i>
                        工作原理
                    </div>
                    <h3 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">YOP MCP 工作原理</h3>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                        了解 YOP MCP 如何在您的开发环境中发挥桥梁作用，连接AI助手与易宝支付开放平台。
                    </p>
                </div>

                <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 md:p-10 shadow-2xl tech-gradient-border border-gray-700 relative overflow-hidden">
                    <!-- Animated background grid -->
                    <div class="absolute inset-0 opacity-5">
                        <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <pattern id="workflowGrid" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
                                    <path d="M 50 0 L 0 0 0 50" fill="none" stroke="white" stroke-width="1" opacity="0.3"/>
                                </pattern>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#workflowGrid)"/>
                        </svg>
                    </div>

                    <!-- Floating particles -->
                    <div class="absolute inset-0 overflow-hidden pointer-events-none">
                        <div class="absolute top-10 left-10 w-2 h-2 bg-blue-400 rounded-full animate-pulse opacity-60"></div>
                        <div class="absolute top-20 right-20 w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse opacity-40" style="animation-delay: 1s;"></div>
                        <div class="absolute bottom-20 left-1/4 w-1 h-1 bg-green-400 rounded-full animate-pulse opacity-50" style="animation-delay: 2s;"></div>
                        <div class="absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse opacity-30" style="animation-delay: 0.5s;"></div>
                    </div>

                    <div class="flex flex-col lg:flex-row items-center justify-around space-y-10 lg:space-y-0 lg:space-x-6 text-white relative z-10">
                        <!-- Step 1 -->
                        <div class="flex flex-col items-center text-center group workflow-icon-bg">
                            <div class="w-20 h-20 bg-blue-500/30 rounded-full flex items-center justify-center mb-3 border-2 border-blue-400 backdrop-blur-sm transition-all duration-300 group-hover:scale-110 group-hover:bg-blue-500/50 group-hover:border-blue-300">
                                <i class="fas fa-user-cog text-blue-300 text-3xl transition-transform duration-300 group-hover:scale-110"></i>
                            </div>
                            <h4 class="font-semibold mb-1 transition-colors duration-300 group-hover:text-blue-300">开发者 + IDE</h4>
                            <p class="text-sm text-gray-400 transition-colors duration-300 group-hover:text-gray-300">在您熟悉的开发环境中</p>
                        </div>

                        <!-- Animated arrow 1 -->
                        <div class="lg:block hidden">
                            <i class="fas fa-arrow-right text-gray-500 text-2xl transform transition-all duration-500 hover:text-blue-400 hover:scale-110"></i>
                        </div>
                        <div class="lg:hidden block">
                            <i class="fas fa-arrow-down text-gray-500 text-2xl transform transition-all duration-500 hover:text-blue-400 hover:scale-110"></i>
                        </div>

                        <!-- Step 2 -->
                        <div class="flex flex-col items-center text-center group workflow-icon-bg">
                            <div class="w-20 h-20 bg-green-500/30 rounded-full flex items-center justify-center mb-3 border-2 border-green-400 backdrop-blur-sm transition-all duration-300 group-hover:scale-110 group-hover:bg-green-500/50 group-hover:border-green-300">
                                <i class="fas fa-comments text-green-300 text-3xl transition-transform duration-300 group-hover:scale-110"></i>
                            </div>
                            <h4 class="font-semibold mb-1 transition-colors duration-300 group-hover:text-green-300">自然语言提示</h4>
                            <p class="text-sm text-gray-400 transition-colors duration-300 group-hover:text-gray-300">用人类语言描述需求</p>
                        </div>

                        <!-- Animated arrow 2 -->
                        <div class="lg:block hidden">
                            <i class="fas fa-arrow-right text-gray-500 text-2xl transform transition-all duration-500 hover:text-green-400 hover:scale-110"></i>
                        </div>
                        <div class="lg:hidden block">
                            <i class="fas fa-arrow-down text-gray-500 text-2xl transform transition-all duration-500 hover:text-green-400 hover:scale-110"></i>
                        </div>

                        <!-- Step 3 -->
                        <div class="flex flex-col items-center text-center group workflow-icon-bg">
                            <div class="w-20 h-20 bg-purple-500/30 rounded-full flex items-center justify-center mb-3 border-2 border-purple-400 backdrop-blur-sm transition-all duration-300 group-hover:scale-110 group-hover:bg-purple-500/50 group-hover:border-purple-300">
                                <i class="fas fa-brain text-purple-300 text-3xl transition-transform duration-300 group-hover:scale-110"></i>
                            </div>
                            <h4 class="font-semibold mb-1 transition-colors duration-300 group-hover:text-purple-300">AI 智能分析</h4>
                            <p class="text-sm text-gray-400 transition-colors duration-300 group-hover:text-gray-300">理解意图并制定方案</p>
                        </div>

                        <!-- Animated arrow 3 -->
                        <div class="lg:block hidden">
                            <i class="fas fa-arrow-right text-gray-500 text-2xl transform transition-all duration-500 hover:text-purple-400 hover:scale-110"></i>
                        </div>
                        <div class="lg:hidden block">
                            <i class="fas fa-arrow-down text-gray-500 text-2xl transform transition-all duration-500 hover:text-purple-400 hover:scale-110"></i>
                        </div>

                        <!-- Step 4 -->
                        <div class="flex flex-col items-center text-center group workflow-icon-bg">
                            <div class="w-20 h-20 bg-orange-500/30 rounded-full flex items-center justify-center mb-3 border-2 border-orange-400 backdrop-blur-sm transition-all duration-300 group-hover:scale-110 group-hover:bg-orange-500/50 group-hover:border-orange-300">
                                <i class="fas fa-cogs text-orange-300 text-3xl transition-transform duration-300 group-hover:scale-110 group-hover:rotate-180"></i>
                            </div>
                            <h4 class="font-semibold mb-1 transition-colors duration-300 group-hover:text-orange-300">YOP MCP</h4>
                            <p class="text-sm text-gray-400 transition-colors duration-300 group-hover:text-gray-300">本地工具集执行</p>
                        </div>

                        <!-- Animated arrow 4 -->
                        <div class="lg:block hidden">
                            <i class="fas fa-arrow-right text-gray-500 text-2xl transform transition-all duration-500 hover:text-orange-400 hover:scale-110"></i>
                        </div>
                        <div class="lg:hidden block">
                            <i class="fas fa-arrow-down text-gray-500 text-2xl transform transition-all duration-500 hover:text-orange-400 hover:scale-110"></i>
                        </div>

                        <!-- Step 5 -->
                        <div class="flex flex-col items-center text-center group workflow-icon-bg">
                            <div class="w-20 h-20 bg-red-500/30 rounded-full flex items-center justify-center mb-3 border-2 border-red-400 backdrop-blur-sm transition-all duration-300 group-hover:scale-110 group-hover:bg-red-500/50 group-hover:border-red-300">
                                <i class="fas fa-cloud-upload-alt text-red-300 text-3xl transition-transform duration-300 group-hover:scale-110"></i>
                            </div>
                            <h4 class="font-semibold mb-1 transition-colors duration-300 group-hover:text-red-300">YOP 开放平台</h4>
                            <p class="text-sm text-gray-400 transition-colors duration-300 group-hover:text-gray-300">获取最新API文档</p>
                        </div>
                    </div>

                    <!-- Enhanced details section -->
                    <div class="mt-10 bg-gray-700/30 rounded-lg p-6 border border-gray-600/50 backdrop-blur-sm">
                        <div class="grid md:grid-cols-3 gap-6 text-center">
                            <div class="group">
                                <div class="w-10 h-10 bg-blue-500/50 rounded-full flex items-center justify-center mx-auto mb-2 border border-blue-400 transition-all duration-300 group-hover:scale-110 group-hover:bg-blue-500/70">
                                    <i class="fas fa-terminal text-blue-300 transition-transform duration-300 group-hover:scale-110"></i>
                                </div>
                                <h5 class="font-semibold text-white mb-1 transition-colors duration-300 group-hover:text-blue-300">标准输入输出</h5>
                                <p class="text-xs text-gray-400 transition-colors duration-300 group-hover:text-gray-300">通过 stdin/stdout 与 `uv yop-mcp` 进程安全通信</p>
                            </div>
                            <div class="group">
                                <div class="w-10 h-10 bg-green-500/50 rounded-full flex items-center justify-center mx-auto mb-2 border border-green-400 transition-all duration-300 group-hover:scale-110 group-hover:bg-green-500/70">
                                    <i class="fas fa-shield-alt text-green-300 transition-transform duration-300 group-hover:scale-110"></i>
                                </div>
                                <h5 class="font-semibold text-white mb-1 transition-colors duration-300 group-hover:text-green-300">本地执行</h5>
                                <p class="text-xs text-gray-400 transition-colors duration-300 group-hover:text-gray-300">所有敏感操作在您的本地环境中完成</p>
                            </div>
                            <div class="group">
                                <div class="w-10 h-10 bg-purple-500/50 rounded-full flex items-center justify-center mx-auto mb-2 border border-purple-400 transition-all duration-300 group-hover:scale-110 group-hover:bg-purple-500/70">
                                    <i class="fas fa-sync-alt text-purple-300 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-180"></i>
                                </div>
                                <h5 class="font-semibold text-white mb-1 transition-colors duration-300 group-hover:text-purple-300">实时同步</h5>
                                <p class="text-xs text-gray-400 transition-colors duration-300 group-hover:text-gray-300">获取最新的API文档和最佳实践</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Prerequisites Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden" id="prerequisites">
        <!-- Background decorative elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-20 left-20 w-32 h-32 bg-blue-200/20 rounded-full blur-2xl animate-pulse"></div>
            <div class="absolute bottom-32 right-20 w-40 h-40 bg-purple-200/20 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-blue-100/30 to-purple-100/30 rounded-full blur-3xl"></div>

            <!-- Geometric patterns -->
            <svg class="absolute inset-0 w-full h-full opacity-5" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="prereqPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
                        <polygon points="50,0 100,50 50,100 0,50" fill="none" stroke="currentColor" stroke-width="1"/>
                        <circle cx="50" cy="50" r="20" fill="none" stroke="currentColor" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#prereqPattern)"/>
            </svg>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16 fade-in-up">
                <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 text-sm font-medium mb-6">
                    <i class="fas fa-check-circle mr-2"></i>
                    准备工作
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    在开始之前，请确保您已具备
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    为了顺利完成 YOP MCP 的配置和使用，您需要准备以下环境和信息
                </p>
            </div>

            <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mb-12">
                <!-- AI编程环境 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl card-shadow hover-scale p-6 md:p-8 tech-gradient-border group fade-in-up relative overflow-hidden" data-delay="0">
                    <!-- Card background pattern -->
                    <div class="absolute inset-0 opacity-5">
                        <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                            <pattern id="aiPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                                <circle cx="10" cy="10" r="1" fill="currentColor"/>
                            </pattern>
                            <rect width="100%" height="100%" fill="url(#aiPattern)"/>
                        </svg>
                    </div>

                    <div class="relative z-10">
                        <div class="flex items-center mb-5">
                            <div class="w-14 h-14 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center feature-icon-glow shadow-lg" style="--glow-color: 147, 51, 234;">
                                <i class="fas fa-robot text-purple-600 text-2xl transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"></i>
                            </div>
                            <h3 class="ml-4 text-xl font-semibold text-gray-900 group-hover:text-purple-700 transition-colors">AI编程环境</h3>
                        </div>
                        <p class="text-gray-600 mb-4 text-sm">您需要具备以下任一环境：</p>
                        <ul class="space-y-2.5 text-sm">
                            <li class="flex items-start group/item">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2.5 flex-shrink-0 transition-transform group-hover/item:scale-110"></i>
                                <span class="group-hover/item:text-gray-800 transition-colors">支持工具调用的IDE (如Cursor)</span>
                            </li>
                            <li class="flex items-start group/item">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2.5 flex-shrink-0 transition-transform group-hover/item:scale-110"></i>
                                <span class="group-hover/item:text-gray-800 transition-colors">AI编程工具 (如Cline, RooCode)</span>
                            </li>
                            <li class="flex items-start group/item">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2.5 flex-shrink-0 transition-transform group-hover/item:scale-110"></i>
                                <span class="group-hover/item:text-gray-800 transition-colors">通过API调用的LLM (如Claude)</span>
                            </li>
                        </ul>
                        <div class="mt-5 p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-md border-l-4 border-purple-500 shadow-sm">
                            <p class="text-purple-700 text-xs"><i class="fas fa-lightbulb mr-1.5 text-yellow-500"></i>推荐使用支持MCP协议的现代AI开发工具</p>
                        </div>
                    </div>
                </div>

                <!-- Python环境 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl card-shadow hover-scale p-6 md:p-8 tech-gradient-border group fade-in-up relative overflow-hidden" data-delay="1">
                    <!-- Card background pattern -->
                    <div class="absolute inset-0 opacity-5">
                        <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                            <pattern id="pythonPattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse">
                                <path d="M15,5 L25,15 L15,25 L5,15 Z" fill="none" stroke="currentColor" stroke-width="0.5"/>
                            </pattern>
                            <rect width="100%" height="100%" fill="url(#pythonPattern)"/>
                        </svg>
                    </div>

                    <div class="relative z-10">
                        <div class="flex items-center mb-5">
                            <div class="w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center feature-icon-glow shadow-lg" style="--glow-color: 59, 130, 246;">
                                <i class="fab fa-python text-blue-600 text-2xl transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"></i>
                            </div>
                            <h3 class="ml-4 text-xl font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">Python环境</h3>
                        </div>
                        <p class="text-gray-600 mb-4 text-sm">确保您的系统已安装：</p>
                        <ul class="space-y-2.5 text-sm">
                            <li class="flex items-start group/item">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2.5 flex-shrink-0 transition-transform group-hover/item:scale-110"></i>
                                <span class="group-hover/item:text-gray-800 transition-colors">Python 3.8 或更高版本</span>
                            </li>
                            <li class="flex items-start group/item">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2.5 flex-shrink-0 transition-transform group-hover/item:scale-110"></i>
                                <span class="group-hover/item:text-gray-800 transition-colors">包管理工具 uv (推荐) 或 pip</span>
                            </li>
                        </ul>
                        <div class="bg-gradient-to-br from-gray-800 to-gray-900 text-white rounded-lg p-4 mt-5 quick-start-code-block shadow-lg border border-gray-700">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-terminal text-gray-400 mr-2"></i>
                                <span class="text-xs font-medium text-gray-300">快速检查</span>
                                <div class="ml-auto flex space-x-1">
                                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                    <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                </div>
                            </div>
                            <code class="text-sm text-green-400 block whitespace-pre-wrap text-xs font-mono"># 检查Python版本
python --version

# 安装uv (推荐)
curl -LsSf https://astral.sh/uv/install.sh | sh</code>
                        </div>
                    </div>
                </div>

                <!-- 易宝支付商户信息 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl card-shadow hover-scale p-6 md:p-8 tech-gradient-border group fade-in-up relative overflow-hidden" data-delay="2">
                    <!-- Card background pattern -->
                    <div class="absolute inset-0 opacity-5">
                        <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                            <pattern id="merchantPattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse">
                                <rect x="5" y="5" width="15" height="15" fill="none" stroke="currentColor" stroke-width="0.5"/>
                                <circle cx="12.5" cy="12.5" r="3" fill="none" stroke="currentColor" stroke-width="0.3"/>
                            </pattern>
                            <rect width="100%" height="100%" fill="url(#merchantPattern)"/>
                        </svg>
                    </div>

                    <div class="relative z-10">
                        <div class="flex items-center mb-5">
                            <div class="w-14 h-14 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center feature-icon-glow shadow-lg" style="--glow-color: 16, 185, 129;">
                                <i class="fas fa-id-card text-green-600 text-2xl transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"></i>
                            </div>
                            <h3 class="ml-4 text-xl font-semibold text-gray-900 group-hover:text-green-700 transition-colors">商户信息</h3>
                        </div>
                        <p class="text-gray-600 mb-4 text-sm">实际API对接时需要：</p>
                        <ul class="space-y-2.5 text-sm">
                            <li class="flex items-start group/item">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2.5 flex-shrink-0 transition-transform group-hover/item:scale-110"></i>
                                <span class="group-hover/item:text-gray-800 transition-colors">易宝支付商户编号 (merchantNo)</span>
                            </li>
                            <li class="flex items-start group/item">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2.5 flex-shrink-0 transition-transform group-hover/item:scale-110"></i>
                                <span class="group-hover/item:text-gray-800 transition-colors">应用密钥对 (YOP MCP可协助生成)</span>
                            </li>
                            <li class="flex items-start group/item">
                                <i class="fas fa-info-circle text-blue-500 mt-1 mr-2.5 flex-shrink-0 transition-transform group-hover/item:scale-110"></i>
                                <span class="group-hover/item:text-gray-800 transition-colors">测试时可使用沙箱环境数据</span>
                            </li>
                        </ul>
                        <div class="mt-5 p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-md border-l-4 border-blue-500 shadow-sm">
                            <p class="text-blue-700 text-xs"><i class="fas fa-info-circle mr-1.5 text-blue-500"></i>沙箱环境提供模拟数据，方便开发测试</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-8 md:p-12 shadow-xl fade-in-up">
                <div class="max-w-2xl mx-auto">
                    <div class="flex justify-center mb-5">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center"><i class="fas fa-rocket text-white text-3xl"></i></div>
                    </div>
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">准备就绪？让我们开始吧！</h3>
                    <p class="text-blue-100 mb-6">所有准备工作完成后，您就可以开始配置 YOP MCP 并体验 AI 驱动的支付集成了。</p>
                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a href="#quick-start" class="hero-button-primary inline-flex items-center justify-center px-7 py-3 text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-blue-50 transition-colors">
                            <i class="fas fa-arrow-down mr-2"></i>开始配置
                        </a>
                        <a href="pages/quick-start.html" class="hero-button-secondary inline-flex items-center justify-center px-7 py-3 text-base font-medium rounded-lg text-white border-2 border-white hover:bg-white/10 transition-colors">
                            <i class="fas fa-book mr-2"></i>详细教程
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Start Section -->
    <section class="py-20 section-bg-pattern" id="quick-start">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 fade-in-up">
                <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 text-sm font-medium mb-6">
                    <i class="fas fa-rocket mr-2"></i>
                    快速开始
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    只需两步，激活您的 AI 集成超能力！
                </h2>
                 <p class="text-lg text-gray-600 max-w-2xl mx-auto">按照以下步骤快速安装和配置YOP MCP，开启您的智能集成之旅。</p>
            </div>
            <div class="grid lg:grid-cols-2 gap-10">
                <!-- Step 1 -->
                <div class="group fade-in-up" data-delay="0">
                    <div class="bg-white/90 backdrop-blur-sm p-8 rounded-xl card-shadow hover-scale tech-gradient-border h-full relative overflow-hidden">
                        <!-- Background pattern -->
                        <div class="absolute inset-0 opacity-5">
                            <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                                <pattern id="step1Pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
                                    <path d="M20,5 L35,20 L20,35 L5,20 Z" fill="none" stroke="currentColor" stroke-width="0.5"/>
                                </pattern>
                                <rect width="100%" height="100%" fill="url(#step1Pattern)"/>
                            </svg>
                        </div>

                        <div class="relative z-10">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-full flex items-center justify-center font-bold text-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                                    <span class="group-hover:rotate-12 transition-transform duration-300">1</span>
                                </div>
                                <h3 class="ml-4 text-2xl font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">安装 YOP MCP 工具</h3>
                            </div>
                            <p class="text-gray-600 mb-5 text-sm">在您的本地开发环境安装 `yop-mcp` 命令行工具。</p>
                            <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg p-4 relative overflow-hidden quick-start-code-block shadow-xl border border-gray-700">
                                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-blue-500"></div>
                                <div class="flex items-center mb-3">
                                    <div class="flex space-x-1.5">
                                        <div class="w-2.5 h-2.5 bg-red-500 rounded-full animate-pulse"></div>
                                        <div class="w-2.5 h-2.5 bg-yellow-400 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                                        <div class="w-2.5 h-2.5 bg-green-500 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                                    </div>
                                    <span class="ml-auto text-gray-400 text-xs font-mono">Terminal</span>
                                </div>
                                <code class="text-green-400 text-sm block leading-relaxed whitespace-pre-wrap text-xs font-mono">
<span class="text-gray-500"># 创建虚拟环境 (推荐)</span>
python -m venv yop_mcp_env

<span class="text-gray-500"># 激活环境 (Linux/macOS)</span>
source yop_mcp_env/bin/activate
<span class="text-gray-500"># (Windows: yop_mcp_env\Scripts\activate)</span>

<span class="text-gray-500"># 安装 yop-mcp (使用uv)</span>
uv pip install yop-mcp
<span class="text-gray-500"># (或使用pip: pip install yop-mcp)</span></code>
                            </div>
                            <div class="flex items-start p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-md border-l-4 border-blue-500 mt-5 shadow-sm">
                                <i class="fas fa-info-circle text-blue-600 mt-1 mr-2.5"></i>
                                <p class="text-blue-800 text-xs">确保已安装 Python 3.8+ 和 uv/pip。推荐使用虚拟环境隔离依赖。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="group fade-in-up" data-delay="1">
                    <div class="bg-white/90 backdrop-blur-sm p-8 rounded-xl card-shadow hover-scale tech-gradient-border h-full relative overflow-hidden">
                        <!-- Background pattern -->
                        <div class="absolute inset-0 opacity-5">
                            <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                                <pattern id="step2Pattern" x="0" y="0" width="35" height="35" patternUnits="userSpaceOnUse">
                                    <circle cx="17.5" cy="17.5" r="8" fill="none" stroke="currentColor" stroke-width="0.5"/>
                                    <circle cx="17.5" cy="17.5" r="3" fill="currentColor" opacity="0.3"/>
                                </pattern>
                                <rect width="100%" height="100%" fill="url(#step2Pattern)"/>
                            </svg>
                        </div>

                        <div class="relative z-10">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 text-white rounded-full flex items-center justify-center font-bold text-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                                    <span class="group-hover:rotate-12 transition-transform duration-300">2</span>
                                </div>
                                <h3 class="ml-4 text-2xl font-semibold text-gray-900 group-hover:text-purple-700 transition-colors">配置 AI 工具</h3>
                            </div>
                            <p class="text-gray-600 mb-5 text-sm">在您的IDE或AI编程工具中添加MCP服务器配置。</p>
                            <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg p-4 relative overflow-hidden quick-start-code-block shadow-xl border border-gray-700">
                                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-pink-500"></div>
                                <div class="flex items-center mb-3">
                                    <div class="flex space-x-1.5">
                                        <div class="w-2.5 h-2.5 bg-red-500 rounded-full animate-pulse"></div>
                                        <div class="w-2.5 h-2.5 bg-yellow-400 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                                        <div class="w-2.5 h-2.5 bg-green-500 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                                    </div>
                                    <span class="ml-auto text-gray-400 text-xs font-mono">mcp_config.json</span>
                                </div>
                                <code class="text-green-400 text-sm block leading-relaxed whitespace-pre-wrap text-xs font-mono">
{
  <span class="text-sky-400">"mcpServers"</span>: {
    <span class="text-sky-400">"yop-mcp"</span>: { <span class="text-gray-500">// 服务名, 可自定义</span>
      <span class="text-sky-400">"command"</span>: <span class="text-yellow-400">"uv"</span>,
      <span class="text-sky-400">"args"</span>: [ <span class="text-yellow-400">"yop-mcp"</span> ],
      <span class="text-gray-500">// 如果yop-mcp不在PATH中或未使用虚拟环境</span>
      <span class="text-gray-500">// "command": "/path/to/yop_mcp_env/bin/uv",</span>
      <span class="text-gray-500">// "args": ["yop-mcp"],</span>
      <span class="text-gray-500">// 或者直接指定yop-mcp的绝对路径:</span>
      <span class="text-gray-500">// "command": "/path/to/yop_mcp_env/bin/yop-mcp",</span>
      <span class="text-gray-500">// "args": [],</span>
    }
  }
}</code>
                            </div>
                            <div class="flex items-start p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-md border-l-4 border-purple-500 mt-5 shadow-sm">
                                <i class="fas fa-cog text-purple-600 mt-1 mr-2.5"></i>
                                <p class="text-purple-800 text-xs">具体配置方式请参考您AI工具的文档。确保命令能正确启动 `yop-mcp` 进程。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Demo Video Section -->
            <div class="mt-20 fade-in-up" id="demo-video">
                <div class="text-center mb-12">
                    <div class="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-red-100 via-pink-100 to-orange-100 text-red-800 text-sm font-medium mb-6 shadow-lg border border-red-200/50">
                        <i class="fas fa-video mr-2 text-red-600"></i>
                        <span class="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent font-semibold">实战演示</span>
                    </div>
                    <h3 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">观看 YOP MCP 实战演示</h3>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        通过真实案例，了解如何使用 YOP MCP 快速完成支付集成
                    </p>
                </div>

                <div class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-2xl p-8 md:p-12 shadow-2xl relative overflow-hidden border border-gray-700/50">
                    <!-- Enhanced background pattern -->
                    <div class="absolute inset-0 opacity-10">
                        <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <pattern id="videoPattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
                                    <circle cx="30" cy="30" r="2" fill="white" opacity="0.3"/>
                                    <circle cx="15" cy="15" r="1" fill="white" opacity="0.2"/>
                                    <circle cx="45" cy="45" r="1" fill="white" opacity="0.2"/>
                                </pattern>
                                <linearGradient id="videoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:rgb(59,130,246);stop-opacity:0.1"/>
                                    <stop offset="50%" style="stop-color:rgb(147,51,234);stop-opacity:0.1"/>
                                    <stop offset="100%" style="stop-color:rgb(239,68,68);stop-opacity:0.1"/>
                                </linearGradient>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#videoPattern)"/>
                            <rect width="100%" height="100%" fill="url(#videoGradient)"/>
                        </svg>
                    </div>

                    <!-- Video placeholder with enhanced effects -->
                    <div class="aspect-video bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-xl relative overflow-hidden group cursor-pointer demo-video-container border border-gray-600/50 backdrop-blur-sm shadow-2xl">
                        <!-- Animated background -->
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-red-900/20 animate-pulse"></div>

                        <!-- Video thumbnail/placeholder -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="relative">
                                <!-- Outer glow ring -->
                                <div class="absolute inset-0 w-24 h-24 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-ping opacity-20"></div>
                                <!-- Main play button -->
                                <div class="w-20 h-20 bg-gradient-to-br from-red-500 via-pink-500 to-red-600 rounded-full flex items-center justify-center shadow-2xl transform transition-all duration-300 group-hover:scale-110 group-hover:shadow-red-500/50 relative z-10">
                                    <i class="fas fa-play text-white text-2xl ml-1 transition-transform duration-300 group-hover:scale-110 drop-shadow-lg"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced video info overlay -->
                        <div class="absolute bottom-4 left-4 right-4 transform transition-all duration-300 group-hover:translate-y-0 translate-y-2">
                            <div class="bg-black/70 backdrop-blur-md rounded-lg p-4 border border-white/20 shadow-xl video-overlay-enhanced">
                                <h4 class="text-white font-semibold mb-1 flex items-center">
                                    <i class="fas fa-clock mr-2 text-red-400"></i>
                                    5分钟快速上手 YOP MCP
                                </h4>
                                <p class="text-gray-300 text-sm">从安装配置到第一个API调用的完整演示</p>
                                <div class="flex items-center mt-3 text-xs text-gray-400">
                                    <span class="flex items-center mr-4 px-2 py-1 bg-white/10 rounded-full">
                                        <i class="fas fa-eye mr-1 text-blue-400"></i>
                                        1.2K 观看
                                    </span>
                                    <span class="flex items-center px-2 py-1 bg-white/10 rounded-full">
                                        <i class="fas fa-thumbs-up mr-1 text-green-400"></i>
                                        98% 好评
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced gradient overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>

                        <!-- Enhanced floating elements -->
                        <div class="absolute top-4 right-4 w-8 h-8 bg-red-500/30 rounded-full animate-pulse border border-red-400/50"></div>
                        <div class="absolute top-1/4 left-4 w-6 h-6 bg-pink-500/30 rounded-full animate-pulse border border-pink-400/50" style="animation-delay: 1s;"></div>
                        <div class="absolute bottom-1/4 right-8 w-4 h-4 bg-blue-500/30 rounded-full animate-pulse border border-blue-400/50" style="animation-delay: 2s;"></div>
                    </div>

                    <!-- Enhanced video features -->
                    <div class="grid md:grid-cols-3 gap-6 mt-8 relative z-10">
                        <div class="text-center group">
                            <div class="w-14 h-14 bg-gradient-to-br from-blue-500/20 to-blue-600/30 rounded-xl flex items-center justify-center mx-auto mb-4 border border-blue-500/40 transition-all duration-300 group-hover:scale-110 group-hover:bg-blue-500/40 shadow-lg group-hover:shadow-blue-500/25">
                                <i class="fas fa-download text-blue-400 text-xl transition-transform duration-300 group-hover:scale-110 group-hover:text-blue-300"></i>
                            </div>
                            <h5 class="text-white font-semibold mb-2 transition-colors duration-300 group-hover:text-blue-300">安装配置</h5>
                            <p class="text-gray-400 text-sm transition-colors duration-300 group-hover:text-gray-300 leading-relaxed">详细演示安装和配置过程</p>
                        </div>
                        <div class="text-center group">
                            <div class="w-14 h-14 bg-gradient-to-br from-green-500/20 to-green-600/30 rounded-xl flex items-center justify-center mx-auto mb-4 border border-green-500/40 transition-all duration-300 group-hover:scale-110 group-hover:bg-green-500/40 shadow-lg group-hover:shadow-green-500/25">
                                <i class="fas fa-code text-green-400 text-xl transition-transform duration-300 group-hover:scale-110 group-hover:text-green-300"></i>
                            </div>
                            <h5 class="text-white font-semibold mb-2 transition-colors duration-300 group-hover:text-green-300">代码生成</h5>
                            <p class="text-gray-400 text-sm transition-colors duration-300 group-hover:text-gray-300 leading-relaxed">AI如何生成高质量集成代码</p>
                        </div>
                        <div class="text-center group">
                            <div class="w-14 h-14 bg-gradient-to-br from-purple-500/20 to-purple-600/30 rounded-xl flex items-center justify-center mx-auto mb-4 border border-purple-500/40 transition-all duration-300 group-hover:scale-110 group-hover:bg-purple-500/40 shadow-lg group-hover:shadow-purple-500/25">
                                <i class="fas fa-rocket text-purple-400 text-xl transition-transform duration-300 group-hover:scale-110 group-hover:text-purple-300"></i>
                            </div>
                            <h5 class="text-white font-semibold mb-2 transition-colors duration-300 group-hover:text-purple-300">实际测试</h5>
                            <p class="text-gray-400 text-sm transition-colors duration-300 group-hover:text-gray-300 leading-relaxed">在沙箱环境中测试API调用</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-20 text-center fade-in-up">
                <div class="bg-gradient-to-r from-green-400 via-teal-500 to-sky-600 rounded-2xl p-8 md:p-12 shadow-xl relative overflow-hidden">
                    <!-- Enhanced background pattern -->
                    <div class="absolute inset-0 opacity-10">
                        <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <pattern id="successPattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
                                    <path d="M25,10 L40,25 L25,40 L10,25 Z" fill="none" stroke="white" stroke-width="1" opacity="0.3"/>
                                    <circle cx="25" cy="25" r="3" fill="white" opacity="0.2"/>
                                </pattern>
                                <radialGradient id="successGlow" cx="50%" cy="50%" r="50%">
                                    <stop offset="0%" style="stop-color:white;stop-opacity:0.2"/>
                                    <stop offset="100%" style="stop-color:white;stop-opacity:0"/>
                                </radialGradient>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#successPattern)"/>
                            <rect width="100%" height="100%" fill="url(#successGlow)"/>
                        </svg>
                    </div>

                    <!-- Floating success elements -->
                    <div class="absolute top-4 left-4 w-6 h-6 bg-white/20 rounded-full floating-success" style="animation-delay: 0s;"></div>
                    <div class="absolute top-8 right-8 w-4 h-4 bg-white/20 rounded-full floating-success" style="animation-delay: 1s;"></div>
                    <div class="absolute bottom-6 left-8 w-5 h-5 bg-white/20 rounded-full floating-success" style="animation-delay: 2s;"></div>
                    <div class="absolute bottom-4 right-4 w-3 h-3 bg-white/20 rounded-full floating-success" style="animation-delay: 3s;"></div>

                    <div class="max-w-2xl mx-auto relative z-10">
                        <div class="flex justify-center mb-6">
                            <div class="relative pulse-ring">
                                <!-- Outer glow ring -->
                                <div class="absolute inset-0 w-20 h-20 bg-white/30 rounded-full animate-ping"></div>
                                <!-- Success icon container -->
                                <div class="w-16 h-16 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/40 shadow-xl relative z-10">
                                    <i class="fas fa-check-circle text-white text-3xl drop-shadow-lg animate-pulse"></i>
                                </div>
                            </div>
                        </div>
                        <h3 class="text-2xl md:text-3xl font-bold text-white mb-4 drop-shadow-lg">配置完成！开始您的第一个集成</h3>
                        <p class="text-green-50 mb-8 text-lg leading-relaxed drop-shadow-sm">恭喜！您已成功配置 YOP MCP。现在可以开始体验 AI 驱动的支付集成了。</p>

                        <!-- Enhanced action buttons -->
                        <div class="flex flex-col sm:flex-row justify-center gap-4">
                            <a href="pages/first-integration.html" class="group enhanced-button relative overflow-hidden inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-xl text-green-600 bg-white hover:bg-green-50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <div class="absolute inset-0 bg-gradient-to-r from-green-50 to-blue-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <i class="fas fa-code mr-2 relative z-10 transition-transform duration-300 group-hover:scale-110 icon-glow-enhanced"></i>
                                <span class="relative z-10">开始第一个集成</span>
                            </a>
                            <a href="pages/quick-start.html" class="group enhanced-button relative overflow-hidden inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-xl text-white border-2 border-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl backdrop-blur-sm">
                                <div class="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <i class="fas fa-book mr-2 relative z-10 transition-transform duration-300 group-hover:scale-110 icon-glow-enhanced"></i>
                                <span class="relative z-10">查看详细教程</span>
                            </a>
                        </div>

                        <!-- Success stats -->
                        <div class="grid grid-cols-3 gap-4 mt-8 pt-6 border-t border-white/20">
                            <div class="text-center group">
                                <div class="text-2xl font-bold text-white mb-1 success-stats transition-all duration-300 group-hover:scale-110">2</div>
                                <div class="text-xs text-green-100 opacity-80 transition-opacity duration-300 group-hover:opacity-100">配置步骤</div>
                            </div>
                            <div class="text-center group">
                                <div class="text-2xl font-bold text-white mb-1 success-stats transition-all duration-300 group-hover:scale-110" style="animation-delay: 0.5s;">5min</div>
                                <div class="text-xs text-green-100 opacity-80 transition-opacity duration-300 group-hover:opacity-100">快速上手</div>
                            </div>
                            <div class="text-center group">
                                <div class="text-2xl font-bold text-white mb-1 success-stats transition-all duration-300 group-hover:scale-110" style="animation-delay: 1s;">∞</div>
                                <div class="text-xs text-green-100 opacity-80 transition-opacity duration-300 group-hover:opacity-100">AI 可能性</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Stories Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 fade-in-up">
                <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 text-sm font-medium mb-6">
                    <i class="fas fa-users mr-2"></i>
                    成功案例
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">开发者都在用 YOP MCP</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    看看其他开发者如何通过 YOP MCP 提升开发效率，快速完成支付集成
                </p>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16 fade-in-up">
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold gradient-text mb-2 stat-pulse">95%</div>
                    <p class="text-gray-600 text-sm">开发时间节省</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold gradient-text mb-2 stat-pulse">5分钟</div>
                    <p class="text-gray-600 text-sm">平均集成时间</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold gradient-text mb-2 stat-pulse">1000+</div>
                    <p class="text-gray-600 text-sm">成功集成项目</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold gradient-text mb-2 stat-pulse">99.9%</div>
                    <p class="text-gray-600 text-sm">代码准确率</p>
                </div>
            </div>

            <!-- Testimonials -->
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-xl card-shadow hover-scale tech-gradient-border fade-in-up" data-delay="0">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold mr-4">张</div>
                        <div>
                            <h4 class="font-semibold text-gray-900">张开发者</h4>
                            <p class="text-gray-500 text-sm">全栈工程师</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">"以前对接支付接口需要研究文档好几天，现在用 YOP MCP 只需要几句话描述需求，AI 就能生成完整的代码。太神奇了！"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl card-shadow hover-scale tech-gradient-border fade-in-up" data-delay="1">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center text-white font-bold mr-4">李</div>
                        <div>
                            <h4 class="font-semibold text-gray-900">李架构师</h4>
                            <p class="text-gray-500 text-sm">技术负责人</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">"团队使用 YOP MCP 后，支付模块的开发效率提升了 10 倍。现在我们可以把更多时间投入到业务逻辑的优化上。"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl card-shadow hover-scale tech-gradient-border fade-in-up" data-delay="2">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold mr-4">王</div>
                        <div>
                            <h4 class="font-semibold text-gray-900">王创业者</h4>
                            <p class="text-gray-500 text-sm">初创公司 CTO</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">"作为初创公司，我们资源有限。YOP MCP 让我们能够快速集成支付功能，大大缩短了产品上线时间。"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center mt-16 fade-in-up">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-8 md:p-12 shadow-xl">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">加入数千名开发者的行列</h3>
                    <p class="text-blue-100 mb-6 max-w-2xl mx-auto">立即体验 YOP MCP，让 AI 成为您的支付集成助手，开启高效开发新时代</p>
                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a href="#quick-start" class="hero-button-primary btn-glow inline-flex items-center justify-center px-8 py-3.5 text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-blue-50 transition-colors">
                            <i class="fas fa-rocket mr-2"></i>立即开始使用
                        </a>
                        <a href="https://github.com/yop-platform/yop-mcp" target="_blank" class="hero-button-secondary btn-glow inline-flex items-center justify-center px-8 py-3.5 text-base font-medium rounded-lg text-white border-2 border-white hover:bg-white/10 transition-colors">
                            <i class="fab fa-github mr-2"></i>查看源码
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-400 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 shadow-md">
                            <i class="fas fa-cubes text-white text-xl"></i>
                        </div>
                        <span class="text-2xl font-bold text-white">YOP MCP</span>
                    </div>
                    <p class="text-sm leading-relaxed">
                        YOP MCP (模型上下文协议) 是易宝支付为开发者精心打造的创新技术与工具集，
                        让您通过AI助手轻松完成支付集成，加速业务创新。
                    </p>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4 text-lg">核心资源</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="#features" class="hover:text-blue-400 transition-colors">核心功能</a></li>
                        <li><a href="pages/quick-start.html" class="hover:text-blue-400 transition-colors">快速上手指南</a></li>
                        <li><a href="#" class="hover:text-blue-400 transition-colors">API 文档 (YOP)</a></li>
                        <li><a href="#" class="hover:text-blue-400 transition-colors">常见问题 (FAQ)</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4 text-lg">保持联系</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://github.com/yop-platform/yop-mcp" target="_blank" class="hover:text-blue-400 transition-colors"><i class="fab fa-github mr-2"></i>GitHub</a></li>
                        <li><a href="#" class="hover:text-blue-400 transition-colors">技术支持</a></li>
                        <li><a href="#" class="hover:text-blue-400 transition-colors">商务合作</a></li>
                        <li><a href="#" class="hover:text-blue-400 transition-colors">加入我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-8 text-center text-xs">
                <p>&copy; <span id="current-year"></span> 易宝支付. All rights reserved. YOP MCP Project.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-opacity duration-300 opacity-0 z-50">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Hide loading screen after page loads
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.classList.add('fade-out');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }
            }, 1500); // Show loading for 1.5 seconds

            // Smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        // Close mobile menu if open
                        const mobileMenu = document.getElementById('mobile-menu');
                        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                            mobileMenu.classList.add('hidden');
                        }
                    }
                });
            });

            // Scroll-triggered fade-in animations
            const animatedElements = document.querySelectorAll('.fade-in-up');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const delay = parseInt(entry.target.dataset.delay) || 0;
                        setTimeout(() => {
                            entry.target.classList.add('visible');
                        }, delay * 150); // 150ms per delay unit
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });
            animatedElements.forEach(el => observer.observe(el));

            // Copy to clipboard
            document.querySelectorAll('code').forEach(codeBlock => {
                const parent = codeBlock.parentElement;
                if (parent.classList.contains('quick-start-code-block') || (parent.tagName === 'DIV' && (parent.classList.contains('bg-gray-800') || parent.classList.contains('bg-gray-900')))) {
                    if (parent.querySelector('.copy-button')) return;
                    const copyButton = document.createElement('button');
                    copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                    copyButton.className = 'copy-button absolute top-2 right-2 text-gray-400 hover:text-white transition-colors p-2 rounded z-10';
                    copyButton.setAttribute('aria-label', 'Copy code');
                    copyButton.style.cssText = 'position: absolute; top: 8px; right: 8px;';
                    copyButton.addEventListener('click', (e) => {
                        e.preventDefault(); e.stopPropagation();
                        const textToCopy = codeBlock.innerText || codeBlock.textContent;
                        navigator.clipboard.writeText(textToCopy).then(() => {
                            copyButton.innerHTML = '<i class="fas fa-check"></i>';
                            copyButton.classList.add('text-green-400');
                            setTimeout(() => {
                                copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                                copyButton.classList.remove('text-green-400');
                            }, 2000);
                        }).catch(err => console.error('Failed to copy: ', err));
                    });
                    parent.style.position = 'relative';
                    parent.appendChild(copyButton);
                }
            });

            // Enhanced Hero title typing animation with fallback
            const heroTitleElement = document.getElementById('hero-title');
            const heroTitleText = "YOP MCP 来了！<br>AI 赋能，一行提示词接入易宝支付。";

            // Ensure title is visible immediately as fallback
            heroTitleElement.innerHTML = heroTitleText;
            heroTitleElement.style.color = '#1E293B';
            heroTitleElement.style.textShadow = '0 2px 4px rgba(255, 255, 255, 0.8), 0 4px 8px rgba(255, 255, 255, 0.6)';

            // Enhanced typing function with better error handling
            function typeChar(text, element, index) {
                try {
                    if (index < text.length) {
                        const char = text.charAt(index);
                        if (char === '<') {
                            // Handle HTML tags like <br>
                            const tagEnd = text.indexOf('>', index);
                            if (tagEnd !== -1) {
                                const tag = text.substring(index, tagEnd + 1);
                                if (tag === '<br>') {
                                    element.appendChild(document.createElement('br'));
                                }
                                setTimeout(() => typeChar(text, element, tagEnd + 1), 50);
                                return;
                            }
                        }

                        const charSpan = document.createElement('span');
                        charSpan.className = 'char';
                        charSpan.textContent = char;
                        charSpan.style.color = '#1E293B';
                        charSpan.style.textShadow = 'inherit';
                        charSpan.style.animationDelay = `${index * 0.02}s`;
                        element.appendChild(charSpan);

                        setTimeout(() => typeChar(text, element, index + 1), 50);
                    } else {
                        // Animation complete - ensure all text is visible
                        element.style.opacity = '1';
                    }
                } catch (error) {
                    console.warn('Typing animation error:', error);
                    // Fallback: ensure text is visible
                    element.innerHTML = heroTitleText;
                    element.style.opacity = '1';
                }
            }

            // Start typing animation after a delay, but keep fallback text visible
            setTimeout(() => {
                try {
                    heroTitleElement.textContent = ''; // Clear for typing
                    typeChar(heroTitleText, heroTitleElement, 0);
                } catch (error) {
                    console.warn('Failed to start typing animation:', error);
                    // Restore fallback text
                    heroTitleElement.innerHTML = heroTitleText;
                }
            }, 800);


            // Scroll progress bar & Nav scroll effect
            const progressBar = document.createElement('div');
            progressBar.style.cssText = `position:fixed;top:0;left:0;width:0%;height:3px;background:linear-gradient(90deg,#0EA5E9,#6366F1);z-index:9999;transition:width 0.1s ease-out;`;
            document.body.appendChild(progressBar);
            const nav = document.querySelector('nav');
            window.addEventListener('scroll', () => {
                const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
                progressBar.style.width = scrolled + '%';
                if (window.scrollY > 50) nav.classList.add('nav-scrolled');
                else nav.classList.remove('nav-scrolled');

                // Back to top button visibility
                const backToTopButton = document.getElementById('back-to-top');
                if (window.scrollY > 300) {
                    backToTopButton.classList.remove('opacity-0');
                    backToTopButton.classList.add('opacity-100');
                } else {
                    backToTopButton.classList.remove('opacity-100');
                    backToTopButton.classList.add('opacity-0');
                }
            });

            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', () => {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Set current year in footer
            document.getElementById('current-year').textContent = new Date().getFullYear();

            // Back to Top button functionality
            const backToTopButton = document.getElementById('back-to-top');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', () => {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }

            // Enhanced demo video interaction
            const demoVideoContainer = document.querySelector('.demo-video-container');
            if (demoVideoContainer) {
                demoVideoContainer.addEventListener('click', () => {
                    // Simulate video play with enhanced effects
                    const playButton = demoVideoContainer.querySelector('.fa-play');
                    const progressBar = demoVideoContainer.querySelector('.demo-progress');

                    if (playButton) {
                        // Change to pause icon
                        playButton.className = 'fas fa-pause text-white text-2xl ml-1';

                        // Animate progress bar
                        if (progressBar) {
                            progressBar.style.transition = 'width 3s linear';
                            progressBar.style.width = '100%';
                        }

                        // Add playing state visual feedback
                        demoVideoContainer.classList.add('playing');

                        // Reset after "video" ends
                        setTimeout(() => {
                            playButton.className = 'fas fa-play text-white text-2xl ml-1';
                            if (progressBar) {
                                progressBar.style.transition = 'width 0.3s ease';
                                progressBar.style.width = '0%';
                            }
                            demoVideoContainer.classList.remove('playing');
                        }, 3000);
                    }
                });
            }

            // AI Code typing animation
            const aiCodeElement = document.getElementById('ai-generated-code');
            if (aiCodeElement) {
                const codeText = `from yop import YopClient

client = YopClient("your_app_key")
response = client.pay.create({
    "order_id": "12345",
    "amount": 100.00,
    "currency": "CNY"
})
print(f"支付结果: {response.status}")`;

                // Observe when the code block comes into view
                const codeObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                typeCode(codeText, aiCodeElement);
                            }, 1000); // Delay to sync with workflow animation
                            codeObserver.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.5 });

                codeObserver.observe(aiCodeElement);
            }

            function typeCode(text, element) {
                element.textContent = '';
                let index = 0;

                function typeNextChar() {
                    if (index < text.length) {
                        element.textContent += text.charAt(index);
                        index++;
                        setTimeout(typeNextChar, 30); // Typing speed
                    }
                }

                typeNextChar();
            }

            // Stats counter animation
            const statsElements = document.querySelectorAll('.stat-pulse');
            const statsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const finalValue = element.textContent;

                        // Animate numbers
                        if (finalValue.includes('%')) {
                            animateNumber(element, 0, parseInt(finalValue), '%');
                        } else if (finalValue.includes('分钟')) {
                            animateNumber(element, 0, parseInt(finalValue), '分钟');
                        } else if (finalValue.includes('+')) {
                            animateNumber(element, 0, parseInt(finalValue), '+');
                        } else if (finalValue.includes('.')) {
                            animateNumber(element, 0, parseFloat(finalValue), '%');
                        }

                        statsObserver.unobserve(element);
                    }
                });
            }, { threshold: 0.5 });

            statsElements.forEach(el => statsObserver.observe(el));

            function animateNumber(element, start, end, suffix = '') {
                const duration = 2000;
                const increment = (end - start) / (duration / 16);
                let current = start;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= end) {
                        current = end;
                        clearInterval(timer);
                    }

                    if (suffix === '%' && end < 10) {
                        element.textContent = current.toFixed(1) + suffix;
                    } else {
                        element.textContent = Math.floor(current) + suffix;
                    }
                }, 16);
            }

            // Enhanced testimonial interactions
            const testimonialCards = document.querySelectorAll('.testimonial-card, [class*="testimonial"]');
            testimonialCards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    const stars = card.querySelectorAll('.fa-star');
                    stars.forEach((star, index) => {
                        setTimeout(() => {
                            star.style.transform = 'scale(1.2)';
                            star.style.filter = 'drop-shadow(0 0 4px rgba(251, 191, 36, 0.8))';
                        }, index * 100);
                    });
                });

                card.addEventListener('mouseleave', () => {
                    const stars = card.querySelectorAll('.fa-star');
                    stars.forEach(star => {
                        star.style.transform = 'scale(1)';
                        star.style.filter = 'none';
                    });
                });
            });

            // Parallax effect for hero section
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const heroSection = document.querySelector('.hero-pattern');
                if (heroSection) {
                    heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
            });

            // Enhanced button interactions
            const enhancedButtons = document.querySelectorAll('.btn-glow');
            enhancedButtons.forEach(button => {
                button.addEventListener('mouseenter', () => {
                    button.style.transform = 'translateY(-2px) scale(1.02)';
                });

                button.addEventListener('mouseleave', () => {
                    button.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Keyboard navigation enhancement
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    document.body.classList.add('keyboard-navigation');
                }
            });

            document.addEventListener('mousedown', () => {
                document.body.classList.remove('keyboard-navigation');
            });
        });
    </script>

    <!-- Enhanced JavaScript for code copying and interactions -->
    <script>
        // Code copying functionality
        function copyCode(elementId) {
            const codeElement = document.getElementById(elementId);
            const text = codeElement.textContent || codeElement.innerText;

            navigator.clipboard.writeText(text).then(function() {
                // Show success feedback
                const button = event.target.closest('button');
                const icon = button.querySelector('i');
                const originalClass = icon.className;

                icon.className = 'fas fa-check text-xs';
                button.title = '已复制！';

                setTimeout(() => {
                    icon.className = originalClass;
                    button.title = '复制代码';
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            });
        }

        // Enhanced scroll animations
        function enhanceScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in-up').forEach(el => {
                observer.observe(el);
            });
        }

        // Initialize enhanced features
        document.addEventListener('DOMContentLoaded', function() {
            enhanceScrollAnimations();

            // Add hover effects to workflow steps
            document.querySelectorAll('.ai-workflow-step').forEach(step => {
                step.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                step.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // 滚动进度条功能
        function updateScrollProgress() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            document.getElementById('scroll-progress').style.width = scrollPercent + '%';
        }

        // 返回顶部按钮功能
        function setupBackToTop() {
            const backToTopBtn = document.getElementById('back-to-top');

            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.add('visible');
                } else {
                    backToTopBtn.classList.remove('visible');
                }
                updateScrollProgress();
            });

            backToTopBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 初始化所有增强功能
        document.addEventListener('DOMContentLoaded', function() {
            enhanceScrollAnimations();
            setupBackToTop();

            // Add hover effects to workflow steps
            document.querySelectorAll('.ai-workflow-step').forEach(step => {
                step.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                step.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>

    <!-- 返回顶部按钮 -->
    <a href="#" id="back-to-top" class="back-to-top" aria-label="返回顶部">
        <i class="fas fa-arrow-up"></i>
    </a>
</body>
</html>
