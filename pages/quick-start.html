<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速开始 - YOP MCP 开发者指南</title>
    
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 自定义样式 -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
        }
        
        .step-line {
            position: absolute;
            left: 28px;
            top: 48px;
            bottom: 0;
            width: 2px;
            background-color: #E5E7EB;
        }
        
        .step-active .step-line {
            background-color: #2563EB;
        }

        code::-webkit-scrollbar {
            height: 4px;
        }

        code::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        code::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 2px;
        }

        code::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="text-xl font-bold text-gray-900">
                        YOP MCP
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-home text-xl"></i>
                    </a>
                    <a href="https://github.com/yop-platform/yop-mcp" target="_blank" 
                       class="text-gray-500 hover:text-gray-900">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">快速开始</h1>
            <p class="text-xl text-gray-600">仅需几步，让我们开启AI驱动的支付集成之旅</p>
        </div>

        <!-- 步骤说明 -->
        <div class="max-w-4xl mx-auto">
            <!-- 步骤1：创建虚拟环境 -->
            <div class="relative pl-16 mb-12 step-active">
                <div class="step-line"></div>
                <div class="absolute left-0 top-0 w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold">
                    1
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">创建Python虚拟环境</h3>
                    <p class="text-gray-600 mb-4">首先，让我们为YOP MCP创建一个独立的Python虚拟环境：</p>
                    <div class="bg-gray-800 rounded-lg p-4 mb-4 overflow-x-auto">
                        <pre class="text-green-400 text-sm"><code># 创建虚拟环境
python -m venv yop_mcp_env

# Linux/macOS激活环境
source yop_mcp_env/bin/activate

# Windows激活环境
# yop_mcp_env\Scripts\activate</code></pre>
                    </div>
                    <div class="bg-blue-50 border-l-4 border-blue-500 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-600 mt-1"></i>
                            </div>
                            <p class="ml-3 text-blue-700">
                                使用虚拟环境可以避免包依赖冲突，确保YOP MCP在一个干净的环境中运行。
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤2：安装YOP MCP -->
            <div class="relative pl-16 mb-12">
                <div class="step-line"></div>
                <div class="absolute left-0 top-0 w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold">
                    2
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">安装YOP MCP命令行工具</h3>
                    <p class="text-gray-600 mb-4">确保虚拟环境已激活，然后使用uv（推荐）或pip安装yop-mcp：</p>
                    <div class="bg-gray-800 rounded-lg p-4 mb-4 overflow-x-auto">
                        <pre class="text-green-400 text-sm"><code># 使用uv安装（推荐，更快）
uv pip install yop-mcp

# 或使用pip安装
# pip install yop-mcp

# 验证安装
yop-mcp --version</code></pre>
                    </div>
                    <div class="mt-4 bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-600 mb-2">安装成功后，您应该能看到类似这样的输出：</p>
                        <pre class="text-gray-700"><code>yop-mcp 0.1.0</code></pre>
                    </div>
                </div>
            </div>

            <!-- 步骤3：配置AI工具 -->
            <div class="relative pl-16 mb-12">
                <div class="step-line"></div>
                <div class="absolute left-0 top-0 w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold">
                    3
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">配置您的AI编程工具</h3>
                    <p class="text-gray-600 mb-4">选择您使用的AI工具，添加YOP MCP服务配置：</p>
                    
                    <!-- 配置示例tabs -->
                    <div class="mb-6">
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex" aria-label="Tabs">
                                <button class="bg-white inline-block p-4 text-blue-600 border-b-2 border-blue-500 rounded-t-lg">
                                    通用配置
                                </button>
                            </nav>
                        </div>
                        <div class="p-4">
                            <div class="bg-gray-800 rounded-lg p-4 mb-4 overflow-x-auto">
                                <pre class="text-green-400 text-sm"><code>{
  "mcpServers": {
    "yop-mcp": {
      "command": "uv",
      "args": [
        "yop-mcp"
      ]
    }
  }
}</code></pre>
                            </div>
                            <ul class="list-disc pl-5 space-y-2 text-gray-600">
                                <li><strong>mcpServers</strong>: 定义可用的MCP服务</li>
                                <li><strong>yop-mcp</strong>: 您为YOP MCP服务定义的名称</li>
                                <li><strong>command</strong>: 启动命令（使用uv）</li>
                                <li><strong>args</strong>: 传递给命令的参数</li>
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <!-- IDE配置说明 -->
                        <div class="grid md:grid-cols-3 gap-4">
                            <!-- Cursor配置说明 -->
                            <div class="border rounded-lg p-4">
                                <h4 class="font-semibold text-lg mb-2 flex items-center">
                                    <i class="fas fa-cube text-blue-600 mr-2"></i>
                                    Cursor
                                </h4>
                                <ol class="list-decimal pl-5 space-y-2 text-gray-600 text-sm">
                                    <li>打开Cursor设置</li>
                                    <li>找到"AI工具配置"</li>
                                    <li>添加MCP配置</li>
                                </ol>
                            </div>

                            <!-- Cline配置说明 -->
                            <div class="border rounded-lg p-4">
                                <h4 class="font-semibold text-lg mb-2 flex items-center">
                                    <i class="fas fa-terminal text-purple-600 mr-2"></i>
                                    Cline
                                </h4>
                                <ol class="list-decimal pl-5 space-y-2 text-gray-600 text-sm">
                                    <li>找到配置目录</li>
                                    <li>创建MCP配置文件</li>
                                    <li>粘贴配置内容</li>
                                </ol>
                            </div>

                            <!-- RooCode配置说明 -->
                            <div class="border rounded-lg p-4">
                                <h4 class="font-semibold text-lg mb-2 flex items-center">
                                    <i class="fas fa-code text-green-600 mr-2"></i>
                                    RooCode
                                </h4>
                                <ol class="list-decimal pl-5 space-y-2 text-gray-600 text-sm">
                                    <li>进入项目设置</li>
                                    <li>找到"AI工具集成"</li>
                                    <li>添加工具配置</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤4：验证配置 -->
            <div class="relative pl-16">
                <div class="absolute left-0 top-0 w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold">
                    4
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">验证配置</h3>
                    <p class="text-gray-600 mb-4">让我们通过一个简单的测试来验证YOP MCP是否已正确配置：</p>
                    
                    <div class="bg-gray-50 rounded-lg p-6 mb-4">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            </div>
                            <div class="flex-grow">
                                <p class="text-gray-800 p-3 bg-white rounded-lg shadow-sm">
                                    请使用yop-mcp获取易宝支付平台的概览信息
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3 mt-4">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                    <i class="fas fa-robot text-blue-600"></i>
                                </div>
                            </div>
                            <div class="flex-grow">
                                <div class="p-3 bg-white rounded-lg shadow-sm">
                                    <p class="mb-2 text-gray-700">正在调用YOP MCP服务获取平台概览...</p>
                                    <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "action": "yeepay_yop_overview",
  "result": {
    "platform": "YOP开放平台",
    "version": "3.0",
    "capabilities": [
      "支付服务",
      "账户服务",
      "营销服务",
      // ...更多服务
    ]
  }
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 border-l-4 border-green-500 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-600 mt-1"></i>
                            </div>
                            <p class="ml-3 text-green-700">
                                如果您看到类似上面的响应，说明YOP MCP已经成功配置！
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下一步操作 -->
        <div class="mt-12 text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">准备开始您的第一个集成了吗？</h2>
            <div class="flex justify-center space-x-4">
                <a href="first-integration.html" 
                   class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-code mr-2"></i>
                    开始第一个集成
                </a>
                <a href="prerequisites.html" 
                   class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    返回准备工作
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-300 py-12 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="col-span-2">
                    <span class="text-xl font-bold text-white block mb-4">YOP MCP</span>
                    <p class="text-gray-400">
                        YOP MCP (模型上下文协议) 是易宝支付为开发者精心打造的创新技术与工具集，
                        让您通过AI助手轻松完成支付集成。
                    </p>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">资源</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white">开发者文档</a></li>
                        <li><a href="#" class="hover:text-white">API 文档</a></li>
                        <li><a href="#" class="hover:text-white">示例代码</a></li>
                        <li><a href="#" class="hover:text-white">常见问题</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">联系我们</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white">技术支持</a></li>
                        <li><a href="#" class="hover:text-white">商务合作</a></li>
                        <li><a href="#" class="hover:text-white">加入我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>© 2025 易宝支付. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
